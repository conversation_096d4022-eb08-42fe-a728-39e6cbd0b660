package com.example.jobrec.ai

import android.util.Log
import com.example.jobrec.BuildConfig
import com.example.jobrec.chatbot.HuggingFaceService
import com.example.jobrec.Job
import com.example.jobrec.models.JobMatch
import com.example.jobrec.models.MatchCriteria
import com.example.jobrec.User
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class JobMatchingService {
    private val huggingFaceService = HuggingFaceService()
    private val TAG = "JobMatchingService"

    private val huggingFaceToken: String
        get() = BuildConfig.HUGGING_FACE_TOKEN

    suspend fun calculateJobMatch(user: User, job: Job): JobMatch {
        return withContext(Dispatchers.IO) {
            try {
                val matchCriteria = extractMatchCriteria(user)
                val aiMatchResult = getAIMatchAnalysis(matchCriteria, job)
                val ruleBasedMatch = calculateRuleBasedMatch(user, job)

                val finalPercentage = (aiMatchResult.first + ruleBasedMatch) / 2
                val reasoning = aiMatchResult.second

                JobMatch(
                    job = job.copy(
                        matchPercentage = finalPercentage,
                        matchReasoning = reasoning
                    ),
                    matchPercentage = finalPercentage,
                    matchReasoning = reasoning,
                    skillsMatch = calculateSkillsMatch(user.skills, job.requirements),
                    experienceMatch = calculateExperienceMatch(user.experience, job.experienceLevel),
                    educationMatch = calculateEducationMatch(user.education, job.jobField),
                    locationMatch = calculateLocationMatch(user.province, job.province)
                )
            } catch (e: Exception) {
                Log.e(TAG, "Error calculating job match", e)
                val fallbackMatch = calculateRuleBasedMatch(user, job)
                JobMatch(
                    job = job.copy(
                        matchPercentage = fallbackMatch,
                        matchReasoning = "Match calculated using profile analysis"
                    ),
                    matchPercentage = fallbackMatch,
                    matchReasoning = "Match calculated using profile analysis"
                )
            }
        }
    }

    private fun extractMatchCriteria(user: User): MatchCriteria {
        return MatchCriteria(
            skills = user.skills,
            experience = user.experience.map { "${it.position} at ${it.company} (${it.startDate} - ${it.endDate})" },
            education = user.education.map { "${it.degree} in ${it.fieldOfStudy} from ${it.institution}" },
            summary = user.summary,
            location = user.province
        )
    }

    private suspend fun getAIMatchAnalysis(criteria: MatchCriteria, job: Job): Pair<Int, String> {
        return try {
            val prompt = buildMatchPrompt(criteria, job)
            val response = huggingFaceService.generateResponse(prompt, huggingFaceToken)
            parseAIResponse(response)
        } catch (e: Exception) {
            Log.e(TAG, "AI analysis failed, using fallback", e)
            Pair(50, "Unable to perform detailed AI analysis")
        }
    }

    private fun buildMatchPrompt(criteria: MatchCriteria, job: Job): String {
        return """
            Analyze job compatibility and provide a match percentage (0-100) and brief reasoning.

            Candidate Profile:
            - Skills: ${criteria.skills.joinToString(", ")}
            - Experience: ${criteria.experience.joinToString("; ")}
            - Education: ${criteria.education.joinToString("; ")}
            - Summary: ${criteria.summary}
            - Location: ${criteria.location}

            Job Requirements:
            - Title: ${job.title}
            - Field: ${job.jobField}
            - Specialization: ${job.specialization}
            - Experience Level: ${job.experienceLevel}
            - Requirements: ${job.requirements}
            - Location: ${job.province}

            Respond with: "MATCH: [percentage]% - [brief reasoning]"
        """.trimIndent()
    }

    private fun parseAIResponse(response: String): Pair<Int, String> {
        return try {
            val matchRegex = """MATCH:\s*(\d+)%\s*-\s*(.+)""".toRegex()
            val matchResult = matchRegex.find(response)

            if (matchResult != null) {
                val percentage = matchResult.groupValues[1].toInt().coerceIn(0, 100)
                val reasoning = matchResult.groupValues[2].trim()
                Pair(percentage, reasoning)
            } else {
                val percentageRegex = """(\d+)%""".toRegex()
                val percentageMatch = percentageRegex.find(response)
                val percentage = percentageMatch?.groupValues?.get(1)?.toInt()?.coerceIn(0, 100) ?: 50
                Pair(percentage, "AI analysis completed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing AI response", e)
            Pair(50, "Analysis completed with standard matching")
        }
    }

    private fun calculateRuleBasedMatch(user: User, job: Job): Int {
        val skillsScore = calculateSkillsMatch(user.skills, job.requirements)
        val experienceScore = calculateExperienceMatch(user.experience, job.experienceLevel)
        val educationScore = calculateEducationMatch(user.education, job.jobField)
        val locationScore = calculateLocationMatch(user.province, job.province)

        return ((skillsScore * 0.4) + (experienceScore * 0.3) + (educationScore * 0.2) + (locationScore * 0.1)).toInt()
    }

    private fun calculateSkillsMatch(userSkills: List<String>, jobRequirements: String): Int {
        if (userSkills.isEmpty() || jobRequirements.isEmpty()) return 0

        val requirements = jobRequirements.lowercase()
        val matchingSkills = userSkills.count { skill ->
            requirements.contains(skill.lowercase())
        }

        return if (userSkills.isNotEmpty()) {
            ((matchingSkills.toDouble() / userSkills.size) * 100).toInt().coerceAtMost(100)
        } else 0
    }

    private fun calculateExperienceMatch(userExperience: List<com.example.jobrec.Experience>, jobExperienceLevel: String): Int {
        if (userExperience.isEmpty()) return if (jobExperienceLevel.lowercase().contains("entry") || jobExperienceLevel.lowercase().contains("junior")) 70 else 20

        val totalYears = userExperience.sumOf { parseExperienceYears(it.startDate, it.endDate) }

        return when (jobExperienceLevel.lowercase()) {
            "entry level", "junior", "0-1 years" -> if (totalYears <= 2) 100 else 80
            "mid level", "intermediate", "2-5 years" -> if (totalYears in 2..5) 100 else if (totalYears < 2) 60 else 90
            "senior level", "senior", "5+ years" -> if (totalYears >= 5) 100 else if (totalYears >= 3) 70 else 40
            else -> 70
        }
    }

    private fun parseExperienceYears(startDate: String, endDate: String): Int {
        return try {
            val startYear = startDate.substringAfterLast("/").toIntOrNull() ?:
                           startDate.substringAfterLast("-").toIntOrNull() ?:
                           java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)

            val endYear = if (endDate.lowercase().contains("present") || endDate.lowercase().contains("current") || endDate.isEmpty()) {
                java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
            } else {
                endDate.substringAfterLast("/").toIntOrNull() ?:
                endDate.substringAfterLast("-").toIntOrNull() ?:
                java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
            }

            kotlin.math.max(1, endYear - startYear)
        } catch (e: Exception) {
            1
        }
    }

    private fun calculateEducationMatch(userEducation: List<com.example.jobrec.Education>, jobField: String): Int {
        if (userEducation.isEmpty()) return 30

        val relevantEducation = userEducation.any { education ->
            education.fieldOfStudy.lowercase().contains(jobField.lowercase()) ||
            jobField.lowercase().contains(education.fieldOfStudy.lowercase())
        }

        return if (relevantEducation) 90 else 50
    }

    private fun calculateLocationMatch(userLocation: String, jobLocation: String): Int {
        return if (userLocation.lowercase() == jobLocation.lowercase()) 100 else 50
    }
}
